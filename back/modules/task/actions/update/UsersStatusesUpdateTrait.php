<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\update;

use app\back\entities\UserHistory;

trait UsersStatusesUpdateTrait
{
    private function updateUsersStatus(iterable $users, array $toUpdate, ?int $sourceId = null): void
    {
        foreach ($users as $siteUser) {
            $this->totalRows++;
            $this->affectedRows += $this->usersRepo->updateStatus($siteUser, $toUpdate, UserHistory::SOURCE_AUTO, $sourceId);
        }
    }
}
