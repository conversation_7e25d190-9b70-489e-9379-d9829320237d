<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\maintenance;

use app\back\entities\User;
use app\back\modules\task\actions\update\UsersStatusesUpdateTrait;
use app\back\modules\task\BaseTask;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\Users;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

/** Task for adhoc users statuses update */
class UsersStatusesMigrateTask extends BaseTask
{
    use UsersStatusesUpdateTrait;

    private int $siteId;

    public function __construct(
        private readonly ConnectionInterface $db,
        private readonly TaskSiteIdResolver $siteIdResolver,
        private readonly Users $usersRepo,
    ) {
    }

    public function process(): void
    {
        $this->siteId = $this->siteIdResolver->siteId();

        $this->playedToNormal();
        $this->playedToPreNormal();
    }

    private function playedToPreNormal(): void
    {
        $affected = new Query($this->db)
            ->select([
                'u.site_id',
                'u.user_id',
            ])
            ->from(['u' => Users::TABLE_NAME])
            ->where([
                'AND',
                [
                    'u.site_id' => $this->siteId,
                    'u.status' => User::STATUS_PLAYED,
                ],
                ['<', 'status_updated_at', date('Y-m-d H:i:s', strtotime('-7 days', strtotime($this->to)))],
                ['IS DISTINCT FROM', 'u.is_manual_status', true],
            ])
            ->each();

        $this->updateUsersStatus($affected, ['status' => User::STATUS_PRE_NORMAL]);
    }

    private function playedToNormal(): void
    {
        $affected = new Query($this->db)
            ->select([
                'site_id',
                'user_id',
            ])
            ->from(['u' => Users::TABLE_NAME])
            ->where([
                'AND',
                [
                    'site_id' => $this->siteId,
                    'status' => [User::STATUS_PLAYED, User::STATUS_PRE_NORMAL],
                ],
                ['<', 'status_updated_at', date('Y-m-d H:i:s', strtotime('-37 days', strtotime($this->to)))],
                ['IS DISTINCT FROM', 'u.is_manual_status', true],
            ])
            ->each();

        $this->updateUsersStatus($affected, ['status' => User::STATUS_NORMAL]);
    }
}
