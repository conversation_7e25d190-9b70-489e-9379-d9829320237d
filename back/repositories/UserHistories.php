<?php

declare(strict_types=1);

namespace app\back\repositories;

use app\back\components\helpers\Arr;
use app\back\entities\User;
use app\back\entities\UserHistory;

class UserHistories extends BaseRepository
{
    public const string ENTITY_CLASS = UserHistory::class;
    public const string TABLE_NAME = 'users_history';
    public const array PRIMARY_KEY = ['id'];

    public function saveChanges(User $user, array $changes, int $source, ?int $sourceId = null, ?int $employeeId = null): void
    {
        if ($user->status === User::STATUS_FREE && $user->active_status === User::ACTIVE_STATUS_ACTIVE && Arr::allKeysExists($changes, ['status', 'active_status'])) {
            return; // Do not save useless history
        }

        if (array_key_exists(UserHistory::COL_STATUS, $changes)) {
            $newIgnored = in_array($user->status, UserHistory::IGNORE_USER_STATUSES, true);
            $oldIgnored = in_array($changes['status'] ?? null, UserHistory::IGNORE_USER_STATUSES, true);

            if ($oldIgnored && $newIgnored) {
                unset($changes['status']);
            }
        }

        $changedKeys = array_keys(array_intersect_key($changes, UserHistory::COLS));
        $newValues = array_combine($changedKeys, array_map(static fn ($k) => $user->$k, $changedKeys));

        if (empty($newValues)) {
            return;
        }

        if (Arr::anyKeyExists($newValues, [UserHistory::COL_STATUS, UserHistory::COL_ACTIVE_STATUS])) {
            $newValues = array_merge($newValues, [
                UserHistory::COL_STATUS => $user->status,
                UserHistory::COL_ACTIVE_STATUS => $user->active_status,
            ]);
        }

        $userHistory = new UserHistory([
            'site_id' => $user->site_id,
            'user_id' => $user->user_id,
            'datetime' => new \DateTimeImmutable(UserHistory::SQL_NOW_DATETIME),
            'source' => $source,
            'source_id' => $sourceId,
            'changes' => $newValues,
            'employee_id' => $employeeId,
        ]);

        $this->insert($userHistory);
    }

    public function decorateChanges(array $changes): array
    {
        static $operatorsRepo;

        if (!isset($operatorsRepo)) {
            $operatorsRepo = new YhOperators($this->db);
        }

        $result = [];
        foreach ($changes as $col => $newValue) {
            $result[$col] = match ($col) {
                UserHistory::COL_STATUS => User::getStatusById($newValue),
                UserHistory::COL_ACTIVE_STATUS => User::getActiveStatusById($newValue),
                UserHistory::COL_PERSONAL_MANAGER => $operatorsRepo->getNameById($newValue),
                default => $newValue,
            };
        }

        return $result;
    }
}
