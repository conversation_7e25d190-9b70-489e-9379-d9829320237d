<?php

declare(strict_types=1);

namespace app\back\repositories;

use app\back\components\GlobalParams;
use app\back\components\helpers\Arr;
use app\back\components\helpers\Db;
use app\back\entities\User;
use app\back\entities\UserHistory;
use Yiisoft\Db\Command\CommandInterface;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class Users extends BaseRepository
{
    public const string ENTITY_CLASS = User::class;
    public const string TABLE_NAME = 'users';
    public const array PRIMARY_KEY = ['site_id', 'user_id'];

    public function resetCloud(int $siteId, int $userId): int
    {
        return $this->db->createCommand()
            ->update(self::TABLE_NAME, ['cid_reset_at' => 'NOW()'], [
                'site_id' => $siteId,
                'user_id' => $userId,
            ])
            ->execute();
    }

    public function resetCloudSources(int $siteId, int $userId): bool
    {
        return $this->db->createCommand()
            ->update(self::TABLE_NAME, ['source_reset_at' => 'NOW()'], [
                'site_id' => $siteId,
                'user_id' => $userId,
            ])
            ->execute() > 0;
    }

    public function isUserBlacklisted(int $siteId, int $userId): bool
    {
        $bblStatus = (new Query($this->db))
            ->select('bbl_status')
            ->from(self::TABLE_NAME)
            ->where([
                'site_id' => $siteId,
                'user_id' => $userId,
            ])
            ->scalar();

        return in_array($bblStatus, [User::BONUS_BL_STATUS_YES_AUTO, User::BONUS_BL_STATUS_YES_MANUAL], true);
    }

    public static function scoreCase(string $tableAlias = 'u'): string
    {
        $emailLikeMasks = ['%@%visa%', '%@%mastercard%', '%@%mc%', '%@%.gov', '%mm9130040%'];

        $statusesMap = GlobalParams::param(GlobalParams::SCORES);

        if (empty($statusesMap)) {
            throw new \RuntimeException('Invalid (empty) scores config');
        }

        $cases = [];

        foreach ($emailLikeMasks as $mask) {
            $cases[] = "WHEN {$tableAlias}.email ILIKE '$mask' THEN 0";
        }

        foreach (array_reverse($statusesMap, true) as $status => $score) {
            $cases[] = "WHEN {$tableAlias}.status = $status THEN $score";
        }

        return '(CASE ' . implode(' ', $cases) . ' ELSE 0 END)';
    }

    public function resetCloudSourcesCommand(string $tmpUsersTable): CommandInterface
    {
        return $this->db->createCommand()->update(
            self::TABLE_NAME,
            ['source_reset_at' => new Expression('NOW()')],
            "(site_id, user_id) IN (SELECT site_id, user_id FROM $tmpUsersTable)",
        );
    }

    public function updateStatus(array $siteUser, array $toUpdate, int $source, ?int $sourceId = null, ?int $employeeId = null): bool
    {
        static $userHistoriesRepo = new UserHistories($this->db);

        /** @var User $user */
        $user = $this->findOneOr404(Arr::leaveOnlyKeys($siteUser, self::PRIMARY_KEY));

        $toUpdate = Arr::leaveOnlyKeys($toUpdate, ['status', 'active_status', 'is_manual_status']);
        $toUpdate = array_filter($toUpdate, static fn($v, $k) => $v !== null && $user->$k !== $v, ARRAY_FILTER_USE_BOTH);

        if (empty($toUpdate)) {
            return false;
        }

        $oldValues = Arr::leaveOnlyKeys([
            'status' => $user->status,
            'active_status' => $user->active_status,
        ], array_keys($toUpdate));

        $toUpdate['status_updated_at'] = new \DateTimeImmutable(User::SQL_NOW_DATETIME);

        Arr::configure($user, $toUpdate);

        $updated = $this->update($user, array_keys($toUpdate));

        if (!empty($oldValues)) {
            $userHistoriesRepo->saveChanges($user, $oldValues, $source, $sourceId, $employeeId);
        }

        return (bool)$updated;
    }

    public function updatePm(array $siteUser, ?int $pmId, int $source, ?int $employeeId = null): bool
    {
        static $userHistoriesRepo = new UserHistories($this->db);

        /** @var User $user */
        $user = $this->findOneOr404($siteUser);

        $oldPm = $user->personal_manager;
        if ($oldPm === $pmId) {
            return false;
        }
        $user->personal_manager = $pmId ?? User::PERSONAL_MANAGER_NO;

        $updated = $this->update($user, ['personal_manager']);
        $userHistoriesRepo->saveChanges($user, [UserHistory::COL_PERSONAL_MANAGER => $oldPm], $source, null, $employeeId);

        return (bool)$updated;
    }

    public static function genderMfExpression(ConnectionInterface $db, string $tableAlias): string
    {
        return Db::buildCaseMapCondition($db, "$tableAlias.gender", User::GENDERS);
    }
}
