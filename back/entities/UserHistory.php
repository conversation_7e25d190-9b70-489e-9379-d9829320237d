<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\BigIdValidator;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\JsonObjectValidator;

class UserHistory extends BaseEntity
{
    public const string COL_PERSONAL_MANAGER = 'personal_manager';
    public const string COL_STATUS = 'status';
    public const string COL_ACTIVE_STATUS = 'active_status';

    public const int SOURCE_PLAYER = 1;
    public const int SOURCE_AUTO = 2;
    public const int SOURCE_UPLOAD = 3;
    public const int SOURCE_AI = 4;

    public const array COLS = [
        self::COL_PERSONAL_MANAGER => 'Personal manager',
        self::COL_STATUS => 'Status',
        self::COL_ACTIVE_STATUS => 'Active Status',
    ];

    public const array IGNORE_USER_STATUSES = [
        User::STATUS_FREE,
        User::STATUS_PAID,
        User::STATUS_PLAYED,
        User::STATUS_PRE_NORMAL,
    ];

    private const array SOURCES = [
        self::SOURCE_PLAYER => 'Player',
        self::SOURCE_AUTO => 'Auto',
        self::SOURCE_UPLOAD => 'Upload',
        self::SOURCE_AI => 'AI',
    ];

    public int $id;
    #[IdValidator]
    public int $site_id;
    #[BigIdValidator]
    public int $user_id;
    #[DateTimeImmutableValidator]
    public \DateTimeImmutable $datetime;
    #[IdValidator]
    public ?int $employee_id;
    #[JsonObjectValidator]
    public ?array $changes;
    #[IntValidator(0, 2 ** 15 - 1)]
    public ?int $source;
    #[IntValidator]
    public ?int $source_id;

    public static function getSourceById(?int $id): ?string
    {
        return static::SOURCES[$id] ?? null;
    }

    public static function getColNameById(string $id): ?string
    {
        return self::COLS[$id] ?? null;
    }
}
