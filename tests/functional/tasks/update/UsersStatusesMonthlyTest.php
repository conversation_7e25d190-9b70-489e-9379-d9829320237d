<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\update;

use app\back\components\helpers\Date;
use app\back\config\tasks\Res;
use app\back\entities\Country;
use app\back\entities\Rate;
use app\back\entities\Site;
use app\back\entities\User;
use app\back\entities\UserHistory;
use app\back\entities\UserStatusVipThreshold;
use app\back\entities\UserTransaction;
use app\back\modules\task\actions\update\UsersStatusesByRulesTask;
use app\back\repositories\Users;
use app\back\repositories\UserHistories;
use app\back\repositories\UserStatusVipThresholds;
use app\tests\functional\tasks\BaseActionTestCase;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;

#[CoversClass(UsersStatusesByRulesTask::class)]
class UsersStatusesMonthlyTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;

    public static function vipPromotionRuleProvider(): array
    {
        return [
            'generic rule' => [
                'thresholdSiteId' => UserStatusVipThreshold::SITE_ID_ANY,
            ],
            'specific rule' => [
                'thresholdSiteId' => Site::CV,
            ],
        ];
    }

    public static function countrySpecificThresholdProvider(): array
    {
        return [
            'country-specific threshold takes precedence over generic (promotes)' => [
                'userCountry' => Country::DE,
                'specificThresholdCountry' => Country::DE,
                'specificThresholdAmount' => 500,
                'genericThresholdAmount' => 900,
                'transactionAmount' => 600,
                'shouldPromote' => true,
            ],
            'falls back to generic threshold when no country-specific exists' => [
                'userCountry' => Country::DE,
                'specificThresholdCountry' => Country::PL,
                'specificThresholdAmount' => 500,
                'genericThresholdAmount' => 900,
                'transactionAmount' => 600,
                'shouldPromote' => false,
            ],
            'country-specific threshold blocks promotion when generic would allow' => [
                'userCountry' => Country::PL,
                'specificThresholdCountry' => Country::PL,
                'specificThresholdAmount' => 800,
                'genericThresholdAmount' => 500,
                'transactionAmount' => 600,
                'shouldPromote' => false,
            ],
        ];
    }

    public static function thresholdSettingsProvider(): array
    {
        return [
            'inactive rule blocks promotion' => [false, true, 'P30D', 'P5D', false, UserStatusVipThreshold::CHECK_PERIOD_MONTH],
            'active promotion rule with recent transaction promotes' => [true, true, 'P30D', 'P5D', true, UserStatusVipThreshold::CHECK_PERIOD_MONTH],
            'active promotion rule with old transaction blocks' => [true, true, 'P7D', 'P10D', false, UserStatusVipThreshold::CHECK_PERIOD_MONTH],
            'daily check_period ignored by monthly task' => [true, true, 'P30D', 'P5D', false, UserStatusVipThreshold::CHECK_PERIOD_DAY],
            'weekly check_period ignored by monthly task' => [true, true, 'P30D', 'P5D', false, UserStatusVipThreshold::CHECK_PERIOD_WEEK],
        ];
    }

    public static function userDowngradeProvider(): array
    {
        return [
            'ASP to VIP Act In downgrade with low activity' => [
                'initialStatus' => User::STATUS_ASP,
                'initialActiveStatus' => User::ACTIVE_STATUS_ACTIVE,
                'targetStatus' => User::STATUS_VIP,
                'targetActiveStatus' => User::ACTIVE_STATUS_ACT_IN,
                'statusUpdatedAge' => 'P40D', // 40 days ago
                'transactionAmount' => 100,
                'thresholdAmount' => 2000, // Higher threshold for downgrade
                'isActive' => true,
                'period' => 'P30D',
                'shouldDowngrade' => true,
            ],
            'inactive downgrade rule blocks downgrade' => [
                'initialStatus' => User::STATUS_ASP,
                'initialActiveStatus' => User::ACTIVE_STATUS_ACTIVE,
                'targetStatus' => User::STATUS_VIP,
                'targetActiveStatus' => User::ACTIVE_STATUS_ACT_IN,
                'statusUpdatedAge' => 'P40D',
                'transactionAmount' => 100,
                'thresholdAmount' => 2000,
                'isActive' => false,
                'period' => 'P30D',
                'shouldDowngrade' => false,
            ],
            'recent status update prevents downgrade' => [
                'initialStatus' => User::STATUS_ASP,
                'initialActiveStatus' => User::ACTIVE_STATUS_ACTIVE,
                'targetStatus' => User::STATUS_VIP,
                'targetActiveStatus' => User::ACTIVE_STATUS_ACT_IN,
                'statusUpdatedAge' => 'P5D', // Too recent
                'transactionAmount' => 100,
                'thresholdAmount' => 2000,
                'isActive' => true,
                'period' => 'P30D',
                'shouldDowngrade' => false,
            ],
            'VIP Act In to VIP Active downgrade' => [
                'initialStatus' => User::STATUS_VIP,
                'initialActiveStatus' => User::ACTIVE_STATUS_ACT_IN,
                'targetStatus' => User::STATUS_VIP,
                'targetActiveStatus' => User::ACTIVE_STATUS_ACTIVE,
                'statusUpdatedAge' => 'P50D', // Long period
                'transactionAmount' => 80,
                'thresholdAmount' => 1200, // Higher threshold for downgrade
                'isActive' => true,
                'period' => 'P35D',
                'shouldDowngrade' => true,
            ],
            'VIP Active to Normal High downgrade' => [
                'initialStatus' => User::STATUS_VIP,
                'initialActiveStatus' => User::ACTIVE_STATUS_ACTIVE,
                'targetStatus' => User::STATUS_NORMAL,
                'targetActiveStatus' => User::ACTIVE_STATUS_HIGH,
                'statusUpdatedAge' => 'P60D', // Long period
                'transactionAmount' => 50,
                'thresholdAmount' => 1500, // Higher threshold for downgrade
                'isActive' => true,
                'period' => 'P45D',
                'shouldDowngrade' => true,
            ],
            'VIP Active with sufficient activity stays' => [
                'initialStatus' => User::STATUS_VIP,
                'initialActiveStatus' => User::ACTIVE_STATUS_ACTIVE,
                'targetStatus' => User::STATUS_NORMAL,
                'targetActiveStatus' => User::ACTIVE_STATUS_HIGH,
                'statusUpdatedAge' => 'P60D',
                'transactionAmount' => 2000, // High activity
                'thresholdAmount' => 1500,
                'isActive' => true,
                'period' => 'P45D',
                'shouldDowngrade' => false,
            ],
        ];
    }

    public static function userFlagsPromotionProvider(): array
    {
        return [
            'is_ignore blocks promotion' => [true, false],
            'is_blocked blocks promotion' => [false, true],
            'both flags block promotion' => [true, true],
        ];
    }

    public static function userFlagsDowngradeProvider(): array
    {
        return [
            'is_ignore allows downgrade' => [true, false],
            'is_blocked allows downgrade' => [false, true],
            'both flags allow downgrade' => [true, true],
        ];
    }

    public static function vipActInPromotionProvider(): array
    {
        return [
            'VIP Active to VIP Act In promotion' => [
                'initialStatus' => User::STATUS_VIP,
                'initialActiveStatus' => User::ACTIVE_STATUS_ACTIVE,
                'targetStatus' => User::STATUS_VIP,
                'targetActiveStatus' => User::ACTIVE_STATUS_ACT_IN,
                'transactionAmount' => 1500,
                'thresholdAmount' => 1200,
                'shouldPromote' => true,
            ],
            'VIP Active with insufficient activity stays' => [
                'initialStatus' => User::STATUS_VIP,
                'initialActiveStatus' => User::ACTIVE_STATUS_ACTIVE,
                'targetStatus' => User::STATUS_VIP,
                'targetActiveStatus' => User::ACTIVE_STATUS_ACT_IN,
                'transactionAmount' => 800,
                'thresholdAmount' => 1200,
                'shouldPromote' => false,
            ],
        ];
    }

    #[DataProvider('vipPromotionRuleProvider')]
    public function testNormalActiveToVipPromotion(int $thresholdSiteId): void
    {
        $this->haveRates();
        $siteId = Site::CV;
        $res = Res::getResourceNameBySiteId($siteId);

        $siteUser = ['site_id' => $siteId, 'user_id' => self::uniqRuntimeId()];
        $monthBegin = new \DateTimeImmutable(date('Y-m-01'));

        // Create user with normal/active status
        $user = $this->createTestUser($siteUser, $monthBegin);

        // Add initial transaction below threshold
        $this->addUserTransaction($siteUser, $monthBegin, 800, 'P1D');

        // Run task - should not affect user (no threshold yet)
        $this->runTask('update-users-statuses-by-rules-monthly', $res);
        $this->assertUserStatusUnchanged($siteUser, $user);

        // Add threshold rule
        $this->createThreshold(siteId: $thresholdSiteId);

        // Run task - should not affect user (amount still below threshold)
        $this->runTask('update-users-statuses-by-rules-monthly', $res);
        $this->assertUserStatusUnchanged($siteUser, $user);

        // Add another transaction to exceed threshold
        $this->addUserTransaction($siteUser, $monthBegin, 200, 'P20D');

        // Run task - should promote user to VIP
        $this->runTask('update-users-statuses-by-rules-monthly', $res);
        $this->assertUserPromotedToVip($siteUser, $user);
    }

    #[DataProvider('vipActInPromotionProvider')]
    public function testVipActiveToVipActInPromotion(
        int $initialStatus,
        int $initialActiveStatus,
        int $targetStatus,
        int $targetActiveStatus,
        int $transactionAmount,
        int $thresholdAmount,
        bool $shouldPromote
    ): void {
        $this->haveRates();
        $siteId = Site::CV;
        $res = Res::getResourceNameBySiteId($siteId);
        $siteUser = ['site_id' => $siteId, 'user_id' => self::uniqRuntimeId()];
        $monthBegin = new \DateTimeImmutable(date('Y-m-01'));

        // Create VIP Active user
        $user = $this->createTestUser($siteUser, $monthBegin, null, $initialStatus, $initialActiveStatus);

        // Add transaction
        $this->addUserTransaction($siteUser, $monthBegin, $transactionAmount, 'P5D');

        // Create VIP Act In promotion threshold
        $this->createThreshold($targetStatus, $targetActiveStatus, $thresholdAmount);

        // Run task
        $this->runTask('update-users-statuses-by-rules-monthly', $res);

        // Assert result
        if ($shouldPromote) {
            $this->assertUserPromotedToVipActIn($siteUser, $targetStatus, $targetActiveStatus);
        } else {
            $this->assertUserStatusUnchanged($siteUser, $user);
        }
    }

    #[DataProvider('countrySpecificThresholdProvider')]
    public function testCountrySpecificThresholdPrecedence(
        string $userCountry,
        string $specificThresholdCountry,
        int $specificThresholdAmount,
        int $genericThresholdAmount,
        int $transactionAmount,
        bool $shouldPromote
    ): void {
        $this->haveRates();
        $siteId = Site::CV;
        $res = Res::getResourceNameBySiteId($siteId);

        $siteUser = ['site_id' => $siteId, 'user_id' => self::uniqRuntimeId()];
        $monthBegin = new \DateTimeImmutable(date('Y-m-01'));

        // Create user with specific country
        $user = $this->createTestUser($siteUser, $monthBegin, $userCountry);

        // Create country-specific threshold
        $this->createThreshold(amount: $specificThresholdAmount, country: $specificThresholdCountry);

        // Create generic threshold (should be ignored if country-specific exists for user's country)
        $this->createThreshold(amount: $genericThresholdAmount);

        // Add transaction
        $this->addUserTransaction($siteUser, $monthBegin, $transactionAmount, 'P1D');

        // Run task
        $this->runTask('update-users-statuses-by-rules-monthly', $res);

        // Assert result based on expected behavior
        if ($shouldPromote) {
            $this->assertUserPromotedToVip($siteUser, $user);
        } else {
            $this->assertUserStatusUnchanged($siteUser, $user);
        }
    }

    #[DataProvider('thresholdSettingsProvider')]
    public function testThresholdSettings(bool $isActive, bool $isUp, string $period, string $transactionAge, bool $shouldPromote, int $checkPeriod): void
    {
        $this->haveRates();
        $siteId = Site::CV;
        $res = Res::getResourceNameBySiteId($siteId);
        $siteUser = ['site_id' => $siteId, 'user_id' => self::uniqRuntimeId()];
        $monthBegin = new \DateTimeImmutable(date('Y-m-01'));

        $user = $this->createTestUser($siteUser, $monthBegin);
        $this->addUserTransaction($siteUser, $monthBegin, 1000, $transactionAge);
        $this->createThreshold(amount: 500, isActive: $isActive, isUp: $isUp, period: $period, checkPeriod: $checkPeriod);
        $this->runTask('update-users-statuses-by-rules-monthly', $res);

        $shouldPromote ? $this->assertUserPromotedToVip($siteUser, $user) : $this->assertUserStatusUnchanged($siteUser, $user);
    }

    #[DataProvider('userDowngradeProvider')]
    public function testUserDowngrade(
        int $initialStatus,
        int $targetStatus,
        string $statusUpdatedAge,
        int $transactionAmount,
        int $thresholdAmount,
        bool $isActive,
        string $period,
        bool $shouldDowngrade,
        int $initialActiveStatus = User::ACTIVE_STATUS_ACTIVE,
        int $targetActiveStatus = User::ACTIVE_STATUS_ACTIVE
    ): void {
        $this->haveRates();
        $siteId = Site::CV;
        $res = Res::getResourceNameBySiteId($siteId);
        $siteUser = ['site_id' => $siteId, 'user_id' => self::uniqRuntimeId()];
        $monthBegin = new \DateTimeImmutable(date('Y-m-01'));

        $user = $this->createTestUser($siteUser, $monthBegin, null, $initialStatus, $initialActiveStatus, $statusUpdatedAge);
        $this->addUserTransaction($siteUser, $monthBegin, $transactionAmount, 'P10D');
        $this->createThreshold($targetStatus, $targetActiveStatus, $thresholdAmount, $isActive, false, $period);
        $this->runTask('update-users-statuses-by-rules-monthly', $res);

        $shouldDowngrade ? $this->assertUserDowngraded($siteUser, $targetStatus, $targetActiveStatus) : $this->assertUserStatusUnchanged($siteUser, $user);
    }

    #[DataProvider('userFlagsPromotionProvider')]
    public function testUserFlagsBlockPromotion(bool $isIgnore, bool $isBlocked): void
    {
        $this->haveRates();
        $siteId = Site::CV;
        $res = Res::getResourceNameBySiteId($siteId);
        $siteUser = ['site_id' => $siteId, 'user_id' => self::uniqRuntimeId()];
        $monthBegin = new \DateTimeImmutable(date('Y-m-01'));

        // Create Normal Active user with flags
        $user = $this->createTestUserWithFlags($siteUser, $monthBegin, $isIgnore, $isBlocked);

        // Add transaction above threshold
        $this->addUserTransaction($siteUser, $monthBegin, 1000, 'P5D');

        // Create promotion rule
        $this->createThreshold(User::STATUS_VIP, User::ACTIVE_STATUS_ACTIVE, 500, true, true);

        $this->runTask('update-users-statuses-by-rules-monthly', $res);

        // User should NOT be promoted due to flags
        $this->assertUserStatusUnchanged($siteUser, $user);
    }

    #[DataProvider('userFlagsDowngradeProvider')]
    public function testUserFlagsAllowDowngrade(bool $isIgnore, bool $isBlocked): void
    {
        $this->haveRates();
        $siteId = Site::CV;
        $res = Res::getResourceNameBySiteId($siteId);
        $siteUser = ['site_id' => $siteId, 'user_id' => self::uniqRuntimeId()];
        $monthBegin = new \DateTimeImmutable(date('Y-m-01'));

        // Create VIP Active user with flags and old status update
        $user = $this->createTestUserWithFlags(
            $siteUser,
            $monthBegin,
            $isIgnore,
            $isBlocked,
            null,
            User::STATUS_VIP,
            User::ACTIVE_STATUS_ACTIVE,
            'P40D'
        );

        // Add transaction below threshold
        $this->addUserTransaction($siteUser, $monthBegin, 100, 'P10D');

        // Create downgrade rule
        $this->createThreshold(User::STATUS_NORMAL, User::ACTIVE_STATUS_HIGH, 500, true, false);

        $this->runTask('update-users-statuses-by-rules-monthly', $res);

        // User SHOULD be downgraded despite flags
        $this->assertUserDowngraded($siteUser, User::STATUS_NORMAL, User::ACTIVE_STATUS_HIGH);
    }

    public function testNoGeneralRuleWarning(): void
    {
        $this->haveRates();
        $siteId = Site::CV;
        $res = Res::getResourceNameBySiteId($siteId);
        $siteUser = ['site_id' => $siteId, 'user_id' => self::uniqRuntimeId()];
        $monthBegin = new \DateTimeImmutable(date('Y-m-01'));

        // Create user that would be eligible for promotion
        $user = $this->createTestUser($siteUser, $monthBegin);
        $this->addUserTransaction($siteUser, $monthBegin, 1000, 'P5D');

        // Create only site-specific threshold (no general fallback)
        $this->createThreshold(amount: 500, siteId: $siteId);

        // Run task - should work fine with site-specific rule
        $this->runTask('update-users-statuses-by-rules-monthly', $res);
        $this->assertUserPromotedToVip($siteUser, $user);

        // Create another user for different site that has no rules
        $differentSiteId = Site::GGB;
        $differentSiteUser = ['site_id' => $differentSiteId, 'user_id' => self::uniqRuntimeId()];
        $differentUser = $this->createTestUser($differentSiteUser, $monthBegin);
        $this->addUserTransaction($differentSiteUser, $monthBegin, 1000, 'P5D');

        // Run task for different site - should trigger "No general rule" warning
        // Since there's no site-specific rule for GGB and no general rule (SITE_ID_ANY)
        $differentRes = Res::getResourceNameBySiteId($differentSiteId);
        $this->runTask('update-users-statuses-by-rules-monthly', $differentRes);

        // User should remain unchanged due to missing rules
        $this->assertUserStatusUnchanged($differentSiteUser, $differentUser);
    }

    private function createTestUser(
        array $siteUser,
        \DateTimeImmutable $monthBegin,
        ?string $country = null,
        int $status = User::STATUS_NORMAL,
        int $activeStatus = User::ACTIVE_STATUS_ACTIVE,
        string $statusUpdatedAge = 'P1M'
    ): User {
        $userData = [
            ...$siteUser,
            'status' => $status,
            'active_status' => $activeStatus,
            'status_updated_at' => $monthBegin->sub(new \DateInterval($statusUpdatedAge)),
        ];

        if ($country !== null) {
            $userData['country'] = $country;
        }

        return $this->haveUserRecord($userData);
    }

    private function createTestUserWithFlags(
        array $siteUser,
        \DateTimeImmutable $monthBegin,
        bool $isIgnore = false,
        bool $isBlocked = false,
        ?string $country = null,
        int $status = User::STATUS_NORMAL,
        int $activeStatus = User::ACTIVE_STATUS_ACTIVE,
        string $statusUpdatedAge = 'P1M'
    ): User {
        $userData = [
            ...$siteUser,
            'status' => $status,
            'active_status' => $activeStatus,
            'status_updated_at' => $monthBegin->sub(new \DateInterval($statusUpdatedAge)),
            'is_ignore' => $isIgnore,
            'is_blocked' => $isBlocked,
        ];

        if ($country !== null) {
            $userData['country'] = $country;
        }

        return $this->haveUserRecord($userData);
    }

    private function addUserTransaction(array $siteUser, \DateTimeImmutable $monthBegin, int $amount, string $dayInterval): void
    {
        $this->haveUserTransactionRecord([
            ...$siteUser,
            'op_id' => UserTransaction::OP_IN,
            'status' => UserTransaction::STATUS_SUCCESS,
            'updated_at' => $monthBegin->sub(new \DateInterval($dayInterval)),
            'currency' => Rate::EUR,
            'amount_orig' => $amount,
        ]);
    }

    private function createThreshold(
        int $status = User::STATUS_VIP,
        int $activeStatus = User::ACTIVE_STATUS_ACTIVE,
        int $amount = 900,
        bool $isActive = true,
        bool $isUp = true,
        string $period = 'P30D',
        int $checkPeriod = UserStatusVipThreshold::CHECK_PERIOD_MONTH,
        int $siteId = UserStatusVipThreshold::SITE_ID_ANY,
        string $country = Country::DEFAULT
    ): UserStatusVipThreshold {
        /** @noinspection PhpIncompatibleReturnTypeInspection */
        return $this->haveRecord(UserStatusVipThresholds::class, [
            'country' => $country,
            'site_id' => $siteId,
            'status' => $status,
            'active_status' => $activeStatus,
            'amount' => $amount,
            'is_active' => $isActive,
            'is_up' => $isUp,
            'period' => new \DateInterval($period),
            'check_period' => $checkPeriod,
        ]);
    }

    private function assertUserDowngraded(array $siteUser, int $expectedStatus, int $expectedActiveStatus = User::ACTIVE_STATUS_ACTIVE): void
    {
        $this->seeRecordWithFields(Users::class, $siteUser, [
            'status' => $expectedStatus,
            'active_status' => $expectedActiveStatus,
        ]);
    }

    private function assertUserStatusUnchanged(array $siteUser, User $originalUser): void
    {
        $this->seeRecordWithFields(Users::class, $siteUser, [
            'status' => $originalUser->status,
            'active_status' => $originalUser->active_status,
            'status_updated_at' => $originalUser->status_updated_at,
        ]);
    }

    private function assertUserPromotedToVip(array $siteUser, User $originalUser): void
    {
        /** @var User $actualUser */
        $actualUser = $this->seeRecordWithFields(Users::class, $siteUser, [
            'status' => User::STATUS_VIP,
            'active_status' => User::ACTIVE_STATUS_ACTIVE,
        ]);

        static::assertGreaterThan($originalUser->status_updated_at, $actualUser->status_updated_at);
    }

    private function assertUserPromotedToVipActIn(array $siteUser, int $expectedStatus, int $expectedActiveStatus): void
    {
        $this->seeRecordWithFields(Users::class, $siteUser, [
            'status' => $expectedStatus,
            'active_status' => $expectedActiveStatus,
        ]);
    }

    public function testTwoThresholdsWithDifferentPeriods(): void
    {
        $this->haveRates();
        $siteId = Site::CV;
        $res = Res::getResourceNameBySiteId($siteId);

        $siteUser = ['site_id' => $siteId, 'user_id' => self::uniqRuntimeId()];
        $monthBegin = new \DateTimeImmutable(date('Y-m-01'));
        $twoMonthAgo = new \DateTimeImmutable(Date::twoMonthsAgo());

        // Create user with normal/active status
        $user = $this->createTestUser($siteUser, $twoMonthAgo);

        // Add threshold rule
        $this->createThreshold(amount: 1000, period: 'P30D');

        // Add initial transaction below threshold
        $this->addUserTransaction($siteUser, $monthBegin, 800, 'P1D');

        // Run task - should not affect user (amount still below threshold)
        $this->runTask('update-users-statuses-by-rules-monthly', $res);
        $this->assertUserStatusUnchanged($siteUser, $user);

        // Add +1 threshold rule
        $this->createThreshold(amount: 2200, period: 'P90D');

        // Add transaction above threshold with period P30D
        $this->addUserTransaction($siteUser, $monthBegin, 800, 'P1D');

        // Run task - should promote user to VIP
        $this->runTask('update-users-statuses-by-rules-monthly', $res);
        $this->assertUserPromotedToVip($siteUser, $user);
    }

    public function testSourceIdSavedInUserHistory(): void
    {
        $this->haveRates();
        $siteId = Site::CV;
        $res = Res::getResourceNameBySiteId($siteId);

        $siteUser = ['site_id' => $siteId, 'user_id' => self::uniqRuntimeId()];
        $monthBegin = new \DateTimeImmutable(date('Y-m-01'));

        // Create user with normal/active status
        $user = $this->createTestUser($siteUser, $monthBegin);

        // Add transaction above threshold
        $this->addUserTransaction($siteUser, $monthBegin, 1000, 'P5D');

        // Create threshold rule for promotion
        $threshold = $this->createThreshold(User::STATUS_VIP, User::ACTIVE_STATUS_ACTIVE, 500);
        $thresholdId = $threshold->id;

        // Run task - should promote user to VIP
        $this->runTask('update-users-statuses-by-rules-monthly', $res);

        // Verify user was promoted
        $this->assertUserPromotedToVip($siteUser, $user);

        // Verify history record was created with correct source_id
        $this->seeRecordWithFields(UserHistories::class, $siteUser, [
            'changes' => ['status' => User::STATUS_VIP, 'active_status' => User::ACTIVE_STATUS_ACTIVE],
            'source' => UserHistory::SOURCE_AUTO,
            'source_id' => $thresholdId,
        ]);

        // Test downgrade scenario
        $downgradeSiteUser = ['site_id' => $siteId, 'user_id' => self::uniqRuntimeId()];
        $downgradeUser = $this->createTestUser(
            $downgradeSiteUser,
            $monthBegin,
            null,
            User::STATUS_VIP,
            User::ACTIVE_STATUS_ACTIVE,
            'P40D' // Old status update to allow downgrade
        );

        // Add low transaction for downgrade
        $this->addUserTransaction($downgradeSiteUser, $monthBegin, 100, 'P10D');

        // Create downgrade threshold rule
        $downgradeThreshold = $this->createThreshold(
            User::STATUS_NORMAL,
            User::ACTIVE_STATUS_HIGH,
            500,
            true,
            false // is_up = false for downgrade
        );
        $downgradeThresholdId = $downgradeThreshold->id;

        // Run task - should downgrade user
        $this->runTask('update-users-statuses-by-rules-monthly', $res);

        // Verify user was downgraded
        $this->assertUserDowngraded($downgradeSiteUser, User::STATUS_NORMAL, User::ACTIVE_STATUS_HIGH);

        // Verify downgrade history record was created with correct source_id
        $this->seeRecordWithFields(UserHistories::class, $downgradeSiteUser, [
            'changes' => ['status' => User::STATUS_NORMAL, 'active_status' => User::ACTIVE_STATUS_HIGH],
            'source' => UserHistory::SOURCE_AUTO,
            'source_id' => $downgradeThresholdId,
        ]);
    }
}
